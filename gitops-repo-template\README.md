# Rails DevOps GitOps Repository

This repository contains Kubernetes manifests for the Rails DevOps project, managed by ArgoCD.

## Repository Structure

```
├── k8s/                          # Kubernetes manifests
│   ├── namespace.yaml            # Application namespace
│   ├── configmap.yaml            # Application configuration
│   ├── secret.yaml               # Sensitive configuration
│   ├── postgres-init-configmap.yaml  # PostgreSQL initialization
│   ├── postgres-statefulset.yaml # PostgreSQL StatefulSet
│   ├── postgres-service.yaml     # PostgreSQL service
│   ├── rails-deployment.yaml     # Rails application deployment
│   ├── rails-service.yaml        # Rails application service
│   ├── ingress.yaml              # Ingress configuration
│   └── kustomization.yaml        # Kustomize configuration
├── environments/                 # Environment-specific configurations
│   ├── development/              # Development environment
│   ├── staging/                  # Staging environment
│   └── production/               # Production environment
└── README.md                     # This file
```

## ArgoCD Application

The ArgoCD application `rails-app` is configured to:
- Monitor this repository for changes
- Sync automatically when changes are detected
- Deploy to the `rails-app` namespace
- Use Kustomize for manifest management

## Application Components

### PostgreSQL Database
- **Type:** StatefulSet for data persistence
- **Image:** postgres:14
- **Storage:** 1Gi persistent volume
- **Service:** ClusterIP on port 5432

### Rails Application
- **Type:** Deployment with 2 replicas
- **Image:** rails-app:latest
- **Init Container:** Database migration and seeding
- **Health Checks:** HTTP probes on `/health` endpoint

### Networking
- **Service:** ClusterIP for internal communication
- **Ingress:** NGINX ingress for external access
- **Host:** rails-app.local

## Deployment Process

1. **Code Changes:** Developers push code changes to the main application repository
2. **CI/CD Pipeline:** Tekton pipeline builds new Docker image and pushes to registry
3. **GitOps Update:** Pipeline updates the image tag in this repository
4. **ArgoCD Sync:** ArgoCD detects changes and deploys to Kubernetes
5. **Health Checks:** Application health is monitored and reported

## Manual Operations

### Updating Image Tag
```bash
# Update the image tag in kustomization.yaml
sed -i 's/rails-app:old-tag/rails-app:new-tag/g' k8s/kustomization.yaml
git add k8s/kustomization.yaml
git commit -m "Update rails-app image to new-tag"
git push
```

### Scaling Application
```bash
# Update replicas in rails-deployment.yaml
# ArgoCD will detect and apply the change
```

### Configuration Changes
```bash
# Update configmap.yaml or secret.yaml
# Commit and push changes
# ArgoCD will sync automatically
```

## Monitoring

- **ArgoCD UI:** Monitor deployment status and sync health
- **Kubernetes Dashboard:** View resource status and logs
- **Application Health:** `/health` endpoint provides application status

## Troubleshooting

### Application Not Syncing
1. Check ArgoCD application status in UI
2. Verify repository credentials
3. Check for syntax errors in YAML files

### Database Connection Issues
1. Verify PostgreSQL StatefulSet is running
2. Check service connectivity
3. Verify credentials in secrets

### Ingress Not Working
1. Ensure NGINX ingress controller is installed
2. Check ingress resource configuration
3. Verify DNS/hosts file configuration

## Security Considerations

- Secrets are base64 encoded (not encrypted)
- Use external secret management for production
- Implement RBAC for ArgoCD access
- Regular security updates for base images
