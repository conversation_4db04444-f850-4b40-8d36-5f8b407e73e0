apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rails-app-ingress
  namespace: rails-app
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  rules:
  - host: rails-app.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rails-app-service
            port:
              number: 80
  # Alternative rule for IP-based access
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rails-app-service
            port:
              number: 80
