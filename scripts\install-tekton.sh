#!/bin/bash

# Install Tekton Pipelines and Dashboard
set -e

echo "=== Installing Tekton Pipelines ==="

# Install Tekton Pipelines
echo "Installing Tekton Pipelines..."
kubectl apply --filename https://storage.googleapis.com/tekton-releases/pipeline/latest/release.yaml

# Wait for Tekton Pipelines to be ready
echo "Waiting for Tekton Pipelines to be ready..."
kubectl wait --for=condition=ready pod --all --timeout=300s -n tekton-pipelines

# Install Tekton Triggers
echo "Installing Tekton Triggers..."
kubectl apply --filename https://storage.googleapis.com/tekton-releases/triggers/latest/release.yaml
kubectl apply --filename https://storage.googleapis.com/tekton-releases/triggers/latest/interceptors.yaml

# Wait for Tekton Triggers to be ready
echo "Waiting for Tekton Triggers to be ready..."
kubectl wait --for=condition=ready pod --all --timeout=300s -n tekton-pipelines

# Install Tekton Dashboard
echo "Installing Tekton Dashboard..."
kubectl apply --filename https://storage.googleapis.com/tekton-releases/dashboard/latest/release.yaml

# Wait for Tekton Dashboard to be ready
echo "Waiting for Tekton Dashboard to be ready..."
kubectl wait --for=condition=ready pod --all --timeout=300s -n tekton-pipelines

echo "=== Tekton Installation Complete ==="
echo ""
echo "Tekton components installed:"
echo "- Tekton Pipelines"
echo "- Tekton Triggers"
echo "- Tekton Dashboard"
echo ""
echo "Access Tekton Dashboard:"
echo "kubectl port-forward -n tekton-pipelines service/tekton-dashboard 9097:9097"
echo "Then open: http://localhost:9097"
echo ""
echo "Next steps:"
echo "1. Configure Docker Hub credentials"
echo "2. Set up pipeline configurations"
echo "3. Create and run pipelines"
