#!/bin/bash

# Build and Deploy Rails App to Kubernetes
set -e

echo "=== Building Docker Image ==="
docker build -t rails-app:latest .

echo "=== Loading Image to Minikube (if using Minikube) ==="
if command -v minikube &> /dev/null; then
    minikube image load rails-app:latest
    echo "Image loaded to Minikube"
fi

echo "=== Installing NGINX Ingress Controller ==="
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml

echo "Waiting for NGINX Ingress Controller to be ready..."
kubectl wait --namespace ingress-nginx \
  --for=condition=ready pod \
  --selector=app.kubernetes.io/component=controller \
  --timeout=300s

echo "=== Deploying Application to Kubernetes ==="
kubectl apply -k k8s/

echo "=== Waiting for PostgreSQL to be ready ==="
kubectl wait --namespace rails-app \
  --for=condition=ready pod \
  --selector=app=postgres \
  --timeout=300s

echo "=== Waiting for Rails App to be ready ==="
kubectl wait --namespace rails-app \
  --for=condition=ready pod \
  --selector=app=rails-app \
  --timeout=300s

echo "=== Deployment Complete ==="
echo ""
echo "To access the application:"
echo "1. Add to /etc/hosts: 127.0.0.1 rails-app.local"
echo "2. Get Ingress IP:"
echo "   kubectl get ingress -n rails-app"
echo "3. Access: http://rails-app.local"
echo ""
echo "Or use port-forward:"
echo "kubectl port-forward -n rails-app service/rails-app-service 3000:80"
echo "Then access: http://localhost:3000"
