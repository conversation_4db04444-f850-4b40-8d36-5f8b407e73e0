apiVersion: triggers.tekton.dev/v1beta1
kind: EventListener
metadata:
  name: rails-app-event-listener
  namespace: tekton-pipelines
spec:
  serviceAccountName: tekton-build-sa
  triggers:
    - name: rails-app-trigger
      bindings:
        - ref: rails-app-trigger-binding
      template:
        ref: rails-app-trigger-template
      interceptors:
        - name: "verify-git-payload"
          ref:
            name: "github"
          params:
            - name: "secretRef"
              value:
                secretName: github-webhook-secret
                secretKey: secretToken
            - name: "eventTypes"
              value: ["push"]
        - name: "only-on-main-branch"
          ref:
            name: "cel"
          params:
            - name: "filter"
              value: "body.ref == 'refs/heads/main'"
---
apiVersion: v1
kind: Service
metadata:
  name: el-rails-app-event-listener
  namespace: tekton-pipelines
  labels:
    app.kubernetes.io/managed-by: EventListener
    app.kubernetes.io/part-of: Triggers
    eventlistener: rails-app-event-listener
spec:
  type: NodePort
  ports:
    - name: http-listener
      port: 8080
      protocol: TCP
      targetPort: 8080
      nodePort: 30081
  selector:
    app.kubernetes.io/managed-by: EventListener
    app.kubernetes.io/part-of: Triggers
    eventlistener: rails-app-event-listener
