apiVersion: v1
kind: Secret
metadata:
  name: private-repo
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repository
type: Opaque
stringData:
  type: git
  url: https://github.com/YOUR_USERNAME/rails-devops-gitops
  # For private repositories, add authentication
  # username: YOUR_GITHUB_USERNAME
  # password: YOUR_GITHUB_TOKEN
  # For SSH access:
  # sshPrivateKey: |
  #   -----BEGIN OPENSSH PRIVATE KEY-----
  #   YOUR_PRIVATE_KEY_HERE
  #   -----END OPENSSH PRIVATE KEY-----
