# Ruby on Rails DevOps Project

This project demonstrates a complete DevOps pipeline for a Ruby on Rails application with PostgreSQL database, including Docker containerization, Kubernetes deployment, ArgoCD GitOps, and Tekton CI/CD.

## Step 1: Docker Setup

### Application Overview
- Simple Ruby on Rails 7.0 application
- PostgreSQL 14 database
- Basic blog functionality with posts
- Health check endpoint
- Containerized with Docker

### Project Structure
```
├── app/                    # Rails application code
├── config/                 # Rails configuration
├── db/                     # Database migrations and seeds
├── Dockerfile              # Rails app container
├── docker-compose.yml      # Multi-container setup
├── docker-entrypoint.sh    # Container startup script
└── README.md              # This file
```

### Running with Docker Compose

1. **Build and start the containers:**
   ```bash
   docker-compose up --build
   ```

2. **Access the application:**
   - Web app: http://localhost:3000
   - Health check: http://localhost:3000/health

3. **Stop the containers:**
   ```bash
   docker-compose down
   ```

### Container Details

#### Rails Application Container
- **Base Image:** ruby:3.1.0
- **Port:** 3000
- **Environment Variables:**
  - `RAILS_ENV=development`
  - `DATABASE_HOST=db`
  - `DATABASE_USERNAME=postgres`
  - `DATABASE_PASSWORD=password`
  - `DATABASE_PORT=5432`

#### PostgreSQL Database Container
- **Image:** postgres:14
- **Port:** 5432
- **Databases:** 
  - `rails_app_development` (primary)
  - `rails_app_test`
  - `rails_app_production`

### Features
- **Health Check Endpoint:** `/health` - Returns application and database status
- **CRUD Operations:** Create, read, update, delete posts
- **Database Migrations:** Automatic on container startup
- **Seed Data:** Sample posts created automatically
- **Volume Persistence:** Database data persisted between container restarts

### Development Commands

```bash
# View logs
docker-compose logs -f web
docker-compose logs -f db

# Execute Rails commands
docker-compose exec web rails console
docker-compose exec web rails db:migrate
docker-compose exec web rails db:seed

# Access database
docker-compose exec db psql -U postgres -d rails_app_development
```

## Step 2: Kubernetes Deployment

### Kubernetes Architecture
- **Namespace:** `rails-app` - Isolated environment for the application
- **PostgreSQL:** StatefulSet with persistent storage
- **Rails App:** Deployment with 2 replicas
- **Services:** ClusterIP services for internal communication
- **Ingress:** NGINX ingress controller for external access
- **ConfigMaps & Secrets:** Environment configuration and sensitive data

### Kubernetes Resources

#### PostgreSQL StatefulSet
- **Image:** postgres:14
- **Storage:** 1Gi persistent volume
- **Service:** `postgres-service` on port 5432
- **Health Checks:** Liveness and readiness probes
- **Init Scripts:** Database creation for multiple environments

#### Rails Application Deployment
- **Image:** rails-app:latest (built from Dockerfile)
- **Replicas:** 2 for high availability
- **Init Container:** Database migration and seeding
- **Health Checks:** HTTP probes on `/health` endpoint
- **Environment:** Production configuration

#### Ingress Configuration
- **Controller:** NGINX Ingress
- **Host:** rails-app.local
- **SSL:** Disabled for local development
- **Path:** Root path (/) routes to Rails service

### Deployment Instructions

#### Prerequisites
- Kubernetes cluster (Minikube, K3d, or cloud provider)
- kubectl configured
- Docker installed

#### Quick Deployment
```bash
# Make scripts executable
chmod +x scripts/*.sh

# Build and deploy everything
./scripts/build-and-deploy.sh
```

#### Manual Deployment
```bash
# 1. Build Docker image
docker build -t rails-app:latest .

# 2. Load image to cluster (for Minikube)
minikube image load rails-app:latest

# 3. Install NGINX Ingress Controller
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml

# 4. Deploy application
kubectl apply -k k8s/

# 5. Wait for deployment
kubectl wait --namespace rails-app --for=condition=ready pod --selector=app=postgres --timeout=300s
kubectl wait --namespace rails-app --for=condition=ready pod --selector=app=rails-app --timeout=300s
```

#### Access the Application
```bash
# Option 1: Using Ingress (add to /etc/hosts: 127.0.0.1 rails-app.local)
# Get ingress IP
kubectl get ingress -n rails-app
# Access: http://rails-app.local

# Option 2: Port forwarding
kubectl port-forward -n rails-app service/rails-app-service 3000:80
# Access: http://localhost:3000

# Option 3: NodePort (for Minikube)
minikube service rails-app-service -n rails-app
```

#### Monitoring and Debugging
```bash
# Check pod status
kubectl get pods -n rails-app

# View logs
kubectl logs -n rails-app deployment/rails-app
kubectl logs -n rails-app statefulset/postgres

# Describe resources
kubectl describe -n rails-app deployment/rails-app
kubectl describe -n rails-app statefulset/postgres

# Access Rails console
kubectl exec -it -n rails-app deployment/rails-app -- rails console

# Access PostgreSQL
kubectl exec -it -n rails-app statefulset/postgres -- psql -U postgres -d rails_app_production
```

#### Cleanup
```bash
# Remove all resources
./scripts/cleanup.sh

# Or manually
kubectl delete -k k8s/
```

## Step 3: ArgoCD GitOps Setup

### ArgoCD Architecture
- **GitOps Repository:** Private GitHub repository containing Kubernetes manifests
- **ArgoCD Server:** Monitors Git repository and syncs changes to Kubernetes
- **Application:** Defines the deployment configuration and sync policies
- **RBAC:** Role-based access control for different user types
- **Ingress:** Web UI access via NGINX ingress controller

### ArgoCD Components

#### Core Configuration Files
- **argocd-cm.yaml:** Main ArgoCD configuration including repository settings
- **argocd-rbac-cm.yaml:** Role-based access control policies
- **repository-secret.yaml:** GitHub repository credentials
- **application.yaml:** Application definition for Rails app deployment

#### Access Configuration
- **argocd-server-service.yaml:** NodePort service for external access
- **argocd-ingress.yaml:** Ingress configuration for web UI

### Installation and Setup

#### Prerequisites
- Kubernetes cluster with NGINX ingress controller
- GitHub account and personal access token
- kubectl configured

#### Quick Setup
```bash
# Set GitHub credentials
export GITHUB_USERNAME=your-username
export GITHUB_TOKEN=your-personal-access-token

# Install ArgoCD
chmod +x scripts/install-argocd.sh
./scripts/install-argocd.sh

# Setup GitOps repository and application
chmod +x scripts/setup-gitops.sh
./scripts/setup-gitops.sh
```

#### Manual Setup

1. **Install ArgoCD:**
   ```bash
   kubectl apply -f argocd/namespace.yaml
   kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
   ```

2. **Apply Configurations:**
   ```bash
   kubectl apply -f argocd/argocd-cm.yaml
   kubectl apply -f argocd/argocd-rbac-cm.yaml
   kubectl apply -f argocd/argocd-server-service.yaml
   kubectl apply -f argocd/argocd-ingress.yaml
   ```

3. **Create GitHub Repository:**
   - Create private repository named `rails-devops-gitops`
   - Copy `k8s/` directory to the repository
   - Push to GitHub

4. **Configure Repository Access:**
   ```bash
   # Update repository-secret.yaml with your credentials
   kubectl apply -f argocd/repository-secret.yaml
   ```

5. **Create Application:**
   ```bash
   # Update application.yaml with your repository URL
   kubectl apply -f argocd/application.yaml
   ```

#### Access ArgoCD UI

```bash
# Get admin password
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d

# Access options:
# 1. Ingress (add to /etc/hosts: 127.0.0.1 argocd.local)
#    http://argocd.local

# 2. Port forwarding
kubectl port-forward svc/argocd-server -n argocd 8080:443
#    https://localhost:8080

# 3. NodePort
#    http://localhost:30080
```

### GitOps Workflow

#### Repository Structure
```
rails-devops-gitops/
├── k8s/                    # Kubernetes manifests
│   ├── namespace.yaml
│   ├── configmap.yaml
│   ├── secret.yaml
│   ├── postgres-*.yaml
│   ├── rails-*.yaml
│   ├── ingress.yaml
│   └── kustomization.yaml
└── README.md
```

#### Deployment Process
1. **Code Changes:** Push to main application repository
2. **CI/CD Pipeline:** Tekton builds and pushes new image
3. **GitOps Update:** Pipeline updates image tag in GitOps repo
4. **ArgoCD Sync:** Detects changes and deploys to Kubernetes
5. **Health Monitoring:** Continuous health checks and reporting

#### Application Management
```bash
# View application status
kubectl get applications -n argocd

# Sync application manually
kubectl patch application rails-app -n argocd --type merge --patch '{"operation":{"initiatedBy":{"username":"admin"},"sync":{"revision":"HEAD"}}}'

# View sync history
kubectl describe application rails-app -n argocd
```

### RBAC Configuration

#### Roles Defined
- **Admin:** Full access to all ArgoCD resources
- **Developer:** Limited access to specific applications
- **ReadOnly:** View-only access to applications and logs

#### User Management
```bash
# Add new user (update argocd-rbac-cm.yaml)
# Example: g, <EMAIL>, role:developer

# Update RBAC configuration
kubectl apply -f argocd/argocd-rbac-cm.yaml
kubectl rollout restart deployment/argocd-server -n argocd
```

### Monitoring and Troubleshooting

#### Health Checks
- **Application Health:** ArgoCD monitors Kubernetes resource health
- **Sync Status:** Shows if repository and cluster are in sync
- **Resource Status:** Individual resource deployment status

#### Common Issues
```bash
# Application not syncing
kubectl describe application rails-app -n argocd

# Repository connection issues
kubectl get secrets -n argocd | grep repo

# Server logs
kubectl logs -n argocd deployment/argocd-server
```

## Step 4: Tekton CI/CD Pipeline

### Tekton Architecture
- **Tekton Pipelines:** Core pipeline execution engine
- **Tekton Dashboard:** Web UI for pipeline management and monitoring
- **Tekton Triggers:** Event-driven pipeline execution via webhooks
- **Tasks:** Reusable pipeline components for specific operations
- **Pipeline:** Orchestrates multiple tasks in sequence or parallel

### Pipeline Components

#### Tasks
1. **git-clone:** Clones source code from GitHub repository
2. **docker-build-push:** Builds Docker image and pushes to Docker Hub
3. **update-gitops:** Updates GitOps repository with new image tag

#### Pipeline Flow
1. **Source Code Checkout:** Clone Rails application from GitHub
2. **Docker Build:** Build container image with commit SHA tag
3. **Image Push:** Push to Docker Hub registry
4. **GitOps Update:** Update Kubernetes manifests with new image tag
5. **ArgoCD Sync:** Automatic deployment via ArgoCD

### Installation and Setup

#### Prerequisites
- Kubernetes cluster with sufficient resources
- Docker Hub account and credentials
- GitHub repositories (source and GitOps)

#### Quick Setup
```bash
# Set required environment variables
export DOCKER_USERNAME=your-docker-username
export DOCKER_PASSWORD=your-docker-password
export DOCKER_EMAIL=<EMAIL>
export GITHUB_USERNAME=your-github-username
export GITHUB_TOKEN=your-github-token

# Install Tekton
chmod +x scripts/install-tekton.sh
./scripts/install-tekton.sh

# Setup pipeline
chmod +x scripts/setup-tekton-pipeline.sh
./scripts/setup-tekton-pipeline.sh
```

#### Manual Installation

1. **Install Tekton Components:**
   ```bash
   # Tekton Pipelines
   kubectl apply -f https://storage.googleapis.com/tekton-releases/pipeline/latest/release.yaml

   # Tekton Triggers
   kubectl apply -f https://storage.googleapis.com/tekton-releases/triggers/latest/release.yaml
   kubectl apply -f https://storage.googleapis.com/tekton-releases/triggers/latest/interceptors.yaml

   # Tekton Dashboard
   kubectl apply -f https://storage.googleapis.com/tekton-releases/dashboard/latest/release.yaml
   ```

2. **Create Docker Credentials:**
   ```bash
   kubectl create secret docker-registry docker-credentials \
     --docker-server=https://index.docker.io/v1/ \
     --docker-username=YOUR_DOCKER_USERNAME \
     --docker-password=YOUR_DOCKER_PASSWORD \
     --docker-email=YOUR_EMAIL \
     --namespace=tekton-pipelines
   ```

3. **Apply Pipeline Resources:**
   ```bash
   kubectl apply -f tekton/service-account.yaml
   kubectl apply -f tekton/tasks/
   kubectl apply -f tekton/pipeline.yaml
   kubectl apply -f tekton/triggers/
   ```

#### Access Tekton Dashboard
```bash
# Port forward to access dashboard
kubectl port-forward -n tekton-pipelines service/tekton-dashboard 9097:9097

# Open in browser
# http://localhost:9097
```

### Pipeline Execution

#### Manual Execution
```bash
# Create a pipeline run
kubectl create -f tekton/pipelinerun.yaml

# Monitor pipeline execution
kubectl get pipelineruns -n tekton-pipelines

# View logs
kubectl logs -f pipelinerun/PIPELINE_RUN_NAME -n tekton-pipelines
```

#### Automatic Execution via Webhook
1. **Get Webhook URL:**
   ```bash
   kubectl get service el-rails-app-event-listener -n tekton-pipelines
   # Use the external IP and port 30081
   ```

2. **Configure GitHub Webhook:**
   - Go to your GitHub repository settings
   - Add webhook: `http://YOUR_CLUSTER_IP:30081`
   - Content type: `application/json`
   - Secret: Use the generated webhook secret
   - Events: Push events

3. **Test Webhook:**
   ```bash
   # Push changes to main branch
   git push origin main

   # Check pipeline execution
   kubectl get pipelineruns -n tekton-pipelines
   ```

### Pipeline Monitoring

#### Dashboard Features
- **Pipeline Runs:** View all pipeline executions and their status
- **Task Runs:** Monitor individual task execution
- **Logs:** Real-time log streaming for debugging
- **Resources:** View pipeline resources and configurations

#### CLI Monitoring
```bash
# List all pipeline runs
kubectl get pipelineruns -n tekton-pipelines

# Describe specific pipeline run
kubectl describe pipelinerun PIPELINE_RUN_NAME -n tekton-pipelines

# View task run logs
kubectl logs -f taskrun/TASK_RUN_NAME -n tekton-pipelines

# Monitor pipeline progress
kubectl get pipelineruns -w -n tekton-pipelines
```

### Troubleshooting

#### Common Issues
1. **Docker Build Failures:**
   ```bash
   # Check Docker credentials
   kubectl get secret docker-credentials -n tekton-pipelines -o yaml

   # Verify service account
   kubectl describe sa tekton-build-sa -n tekton-pipelines
   ```

2. **Git Clone Issues:**
   ```bash
   # Check repository access
   kubectl logs taskrun/git-clone-XXXXX -n tekton-pipelines
   ```

3. **Pipeline Not Triggering:**
   ```bash
   # Check event listener
   kubectl get eventlistener -n tekton-pipelines
   kubectl logs deployment/el-rails-app-event-listener -n tekton-pipelines
   ```

## Complete Project Setup

### One-Command Setup
```bash
# Run the complete setup script
chmod +x scripts/setup-complete-project.sh
./scripts/setup-complete-project.sh
```

This script will:
1. Deploy Rails application with PostgreSQL on Kubernetes
2. Install and configure ArgoCD for GitOps
3. Set up Tekton pipelines for CI/CD
4. Create GitHub repositories
5. Configure webhooks and automation

### Project Structure Summary
```
rails-devops-project/
├── app/                    # Rails application code
├── config/                 # Rails configuration
├── db/                     # Database migrations and seeds
├── k8s/                    # Kubernetes manifests
├── argocd/                 # ArgoCD configuration
├── tekton/                 # Tekton pipeline definitions
├── scripts/                # Deployment and setup scripts
├── Dockerfile              # Container definition
├── docker-compose.yml      # Local development
└── README.md              # This documentation
```

### Access Points
- **Rails App:** http://rails-app.local or port-forward to 3000
- **ArgoCD:** http://argocd.local or port-forward to 8080
- **Tekton Dashboard:** port-forward to 9097
- **Health Check:** http://rails-app.local/health

### Workflow Summary
1. **Development:** Code changes pushed to GitHub
2. **CI/CD:** Tekton pipeline builds and pushes Docker image
3. **GitOps:** Pipeline updates GitOps repository
4. **Deployment:** ArgoCD syncs changes to Kubernetes
5. **Monitoring:** Health checks and dashboard monitoring
