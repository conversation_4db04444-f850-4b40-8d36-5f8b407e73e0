# Ruby on Rails DevOps Project

This project demonstrates a complete DevOps pipeline for a Ruby on Rails application with PostgreSQL database, including Docker containerization, Kubernetes deployment, ArgoCD GitOps, and Tekton CI/CD.

## Step 1: Docker Setup

### Application Overview
- Simple Ruby on Rails 7.0 application
- PostgreSQL 14 database
- Basic blog functionality with posts
- Health check endpoint
- Containerized with Docker

### Project Structure
```
├── app/                    # Rails application code
├── config/                 # Rails configuration
├── db/                     # Database migrations and seeds
├── Dockerfile              # Rails app container
├── docker-compose.yml      # Multi-container setup
├── docker-entrypoint.sh    # Container startup script
└── README.md              # This file
```

### Running with Docker Compose

1. **Build and start the containers:**
   ```bash
   docker-compose up --build
   ```

2. **Access the application:**
   - Web app: http://localhost:3000
   - Health check: http://localhost:3000/health

3. **Stop the containers:**
   ```bash
   docker-compose down
   ```

### Container Details

#### Rails Application Container
- **Base Image:** ruby:3.1.0
- **Port:** 3000
- **Environment Variables:**
  - `RAILS_ENV=development`
  - `DATABASE_HOST=db`
  - `DATABASE_USERNAME=postgres`
  - `DATABASE_PASSWORD=password`
  - `DATABASE_PORT=5432`

#### PostgreSQL Database Container
- **Image:** postgres:14
- **Port:** 5432
- **Databases:** 
  - `rails_app_development` (primary)
  - `rails_app_test`
  - `rails_app_production`

### Features
- **Health Check Endpoint:** `/health` - Returns application and database status
- **CRUD Operations:** Create, read, update, delete posts
- **Database Migrations:** Automatic on container startup
- **Seed Data:** Sample posts created automatically
- **Volume Persistence:** Database data persisted between container restarts

### Development Commands

```bash
# View logs
docker-compose logs -f web
docker-compose logs -f db

# Execute Rails commands
docker-compose exec web rails console
docker-compose exec web rails db:migrate
docker-compose exec web rails db:seed

# Access database
docker-compose exec db psql -U postgres -d rails_app_development
```

## Step 2: Kubernetes Deployment

### Kubernetes Architecture
- **Namespace:** `rails-app` - Isolated environment for the application
- **PostgreSQL:** StatefulSet with persistent storage
- **Rails App:** Deployment with 2 replicas
- **Services:** ClusterIP services for internal communication
- **Ingress:** NGINX ingress controller for external access
- **ConfigMaps & Secrets:** Environment configuration and sensitive data

### Kubernetes Resources

#### PostgreSQL StatefulSet
- **Image:** postgres:14
- **Storage:** 1Gi persistent volume
- **Service:** `postgres-service` on port 5432
- **Health Checks:** Liveness and readiness probes
- **Init Scripts:** Database creation for multiple environments

#### Rails Application Deployment
- **Image:** rails-app:latest (built from Dockerfile)
- **Replicas:** 2 for high availability
- **Init Container:** Database migration and seeding
- **Health Checks:** HTTP probes on `/health` endpoint
- **Environment:** Production configuration

#### Ingress Configuration
- **Controller:** NGINX Ingress
- **Host:** rails-app.local
- **SSL:** Disabled for local development
- **Path:** Root path (/) routes to Rails service

### Deployment Instructions

#### Prerequisites
- Kubernetes cluster (Minikube, K3d, or cloud provider)
- kubectl configured
- Docker installed

#### Quick Deployment
```bash
# Make scripts executable
chmod +x scripts/*.sh

# Build and deploy everything
./scripts/build-and-deploy.sh
```

#### Manual Deployment
```bash
# 1. Build Docker image
docker build -t rails-app:latest .

# 2. Load image to cluster (for Minikube)
minikube image load rails-app:latest

# 3. Install NGINX Ingress Controller
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml

# 4. Deploy application
kubectl apply -k k8s/

# 5. Wait for deployment
kubectl wait --namespace rails-app --for=condition=ready pod --selector=app=postgres --timeout=300s
kubectl wait --namespace rails-app --for=condition=ready pod --selector=app=rails-app --timeout=300s
```

#### Access the Application
```bash
# Option 1: Using Ingress (add to /etc/hosts: 127.0.0.1 rails-app.local)
# Get ingress IP
kubectl get ingress -n rails-app
# Access: http://rails-app.local

# Option 2: Port forwarding
kubectl port-forward -n rails-app service/rails-app-service 3000:80
# Access: http://localhost:3000

# Option 3: NodePort (for Minikube)
minikube service rails-app-service -n rails-app
```

#### Monitoring and Debugging
```bash
# Check pod status
kubectl get pods -n rails-app

# View logs
kubectl logs -n rails-app deployment/rails-app
kubectl logs -n rails-app statefulset/postgres

# Describe resources
kubectl describe -n rails-app deployment/rails-app
kubectl describe -n rails-app statefulset/postgres

# Access Rails console
kubectl exec -it -n rails-app deployment/rails-app -- rails console

# Access PostgreSQL
kubectl exec -it -n rails-app statefulset/postgres -- psql -U postgres -d rails_app_production
```

#### Cleanup
```bash
# Remove all resources
./scripts/cleanup.sh

# Or manually
kubectl delete -k k8s/
```

### Next Steps
- Step 3: ArgoCD GitOps setup
- Step 4: Tekton CI/CD pipeline
