apiVersion: apps/v1
kind: Deployment
metadata:
  name: rails-app
  namespace: rails-app
  labels:
    app: rails-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: rails-app
  template:
    metadata:
      labels:
        app: rails-app
    spec:
      initContainers:
      - name: db-migrate
        image: rails-app:latest
        command: ['sh', '-c']
        args:
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until pg_isready -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME; do
            echo "PostgreSQL is unavailable - sleeping"
            sleep 2
          done
          echo "PostgreSQL is ready!"
          echo "Running database setup..."
          bundle exec rails db:create db:migrate db:seed
        env:
        - name: RAILS_ENV
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: RAILS_ENV
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: DATABASE_PORT
        - name: DATABASE_USERNAME
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: DATABASE_USERNAME
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rails-app-secret
              key: DATABASE_PASSWORD
      containers:
      - name: rails-app
        image: rails-app:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: RAILS_ENV
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: RAILS_ENV
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: DATABASE_PORT
        - name: DATABASE_USERNAME
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: DATABASE_USERNAME
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rails-app-secret
              key: DATABASE_PASSWORD
        - name: RAILS_LOG_TO_STDOUT
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: RAILS_LOG_TO_STDOUT
        - name: RAILS_SERVE_STATIC_FILES
          valueFrom:
            configMapKeyRef:
              name: rails-app-config
              key: RAILS_SERVE_STATIC_FILES
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
