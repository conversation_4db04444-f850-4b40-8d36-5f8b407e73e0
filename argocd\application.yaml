apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: rails-app
  namespace: argocd
  labels:
    app: rails-app
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  
  # Source configuration
  source:
    repoURL: https://github.com/YOUR_USERNAME/rails-devops-gitops
    targetRevision: HEAD
    path: k8s
    
    # Kustomize configuration
    kustomize:
      images:
        - rails-app:latest
  
  # Destination configuration
  destination:
    server: https://kubernetes.default.svc
    namespace: rails-app
  
  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  
  # Health check configuration
  ignoreDifferences:
    - group: apps
      kind: Deployment
      jsonPointers:
        - /spec/replicas
  
  # Revision history limit
  revisionHistoryLimit: 10
