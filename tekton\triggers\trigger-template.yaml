apiVersion: triggers.tekton.dev/v1beta1
kind: TriggerTemplate
metadata:
  name: rails-app-trigger-template
  namespace: tekton-pipelines
spec:
  params:
    - name: git-repo-url
      description: The git repository url
    - name: git-revision
      description: The git revision
      default: main
    - name: git-repo-name
      description: The name of the deployment to be created / patched
    - name: docker-image-name
      description: Docker image name
      default: "YOUR_DOCKER_USERNAME/rails-app"
    - name: gitops-repo-url
      description: GitOps repository URL
      default: "https://github.com/YOUR_USERNAME/rails-devops-gitops"
  
  resourcetemplates:
    - apiVersion: tekton.dev/v1beta1
      kind: PipelineRun
      metadata:
        generateName: rails-app-pipeline-run-
        namespace: tekton-pipelines
        labels:
          app: rails-app
          pipeline: ci-cd
          trigger: webhook
      spec:
        pipelineRef:
          name: rails-app-pipeline
        
        params:
          - name: repo-url
            value: $(tt.params.git-repo-url)
          - name: branch-name
            value: $(tt.params.git-revision)
          - name: image-reference
            value: $(tt.params.docker-image-name)
          - name: dockerfile
            value: "./Dockerfile"
          - name: context
            value: "."
          - name: gitops-repo-url
            value: $(tt.params.gitops-repo-url)
        
        workspaces:
          - name: shared-data
            volumeClaimTemplate:
              spec:
                accessModes:
                  - ReadWriteOnce
                resources:
                  requests:
                    storage: 1Gi
          
          - name: docker-credentials
            secret:
              secretName: docker-credentials
          
          - name: gitops-data
            volumeClaimTemplate:
              spec:
                accessModes:
                  - ReadWriteOnce
                resources:
                  requests:
                    storage: 500Mi
        
        serviceAccountName: tekton-build-sa
        timeout: "1h0m0s"
