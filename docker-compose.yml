version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:14
    container_name: rails_postgres
    environment:
      POSTGRES_DB: rails_app_development
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Rails Application
  web:
    build: .
    container_name: rails_app
    environment:
      RAILS_ENV: development
      DATABASE_HOST: db
      DATABASE_USERNAME: postgres
      DATABASE_PASSWORD: password
      DATABASE_PORT: 5432
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - bundle_cache:/usr/local/bundle
    depends_on:
      db:
        condition: service_healthy
    stdin_open: true
    tty: true

volumes:
  postgres_data:
  bundle_cache:
