# Create sample posts
Post.create!([
  {
    title: "Welcome to Our Rails App",
    content: "This is the first post in our Ruby on Rails application. This app demonstrates a simple blog functionality with PostgreSQL database integration. You can create, read, update, and delete posts through a clean web interface."
  },
  {
    title: "Docker and Kubernetes Ready",
    content: "This application is containerized using Docker and ready for deployment on Kubernetes. It includes proper health checks, database connectivity, and follows best practices for cloud-native applications."
  },
  {
    title: "DevOps Pipeline Integration",
    content: "The application is designed to work seamlessly with modern DevOps tools including ArgoCD for GitOps deployment and Tekton for CI/CD pipelines. This enables automated testing, building, and deployment workflows."
  }
])

puts "Created #{Post.count} sample posts"
