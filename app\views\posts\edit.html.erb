<h1>Edit Post</h1>

<%= form_with model: @post, local: true do |form| %>
  <% if @post.errors.any? %>
    <div class="alert alert-danger">
      <h4><%= pluralize(@post.errors.count, "error") %> prohibited this post from being saved:</h4>
      <ul>
        <% @post.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group">
    <%= form.label :title %>
    <%= form.text_field :title, class: "form-control" %>
  </div>

  <div class="form-group">
    <%= form.label :content %>
    <%= form.text_area :content, rows: 10, class: "form-control" %>
  </div>

  <div class="form-actions">
    <%= form.submit "Update Post", class: "btn btn-primary" %>
    <%= link_to "Show", @post, class: "btn btn-outline" %>
    <%= link_to "Back", posts_path, class: "btn btn-outline" %>
  </div>
<% end %>
