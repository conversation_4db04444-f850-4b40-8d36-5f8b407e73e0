#!/bin/bash

# Setup GitOps repository and ArgoCD application
set -e

# Check if required environment variables are set
if [ -z "$GITHUB_USERNAME" ] || [ -z "$GITHUB_TOKEN" ]; then
    echo "Error: Please set GITHUB_USERNAME and GITHUB_TOKEN environment variables"
    echo "Example:"
    echo "export GITHUB_USERNAME=your-username"
    echo "export GITHUB_TOKEN=your-personal-access-token"
    exit 1
fi

REPO_NAME="rails-devops-gitops"
REPO_URL="https://github.com/$GITHUB_USERNAME/$REPO_NAME"

echo "=== Setting up GitOps Repository ==="
echo "Repository: $REPO_URL"

# Create temporary directory for GitOps repo
TEMP_DIR=$(mktemp -d)
cd $TEMP_DIR

# Initialize git repository
git init
git config user.name "ArgoCD Setup"
git config user.email "<EMAIL>"

# Create directory structure
mkdir -p k8s

# Copy Kubernetes manifests
cp -r $(dirname $0)/../k8s/* k8s/

# Create README for GitOps repo
cat > README.md << EOF
# Rails DevOps GitOps Repository

This repository contains Kubernetes manifests for the Rails DevOps project, managed by ArgoCD.

## Structure
- \`k8s/\` - Kubernetes manifests for the Rails application and PostgreSQL database

## ArgoCD Application
The ArgoCD application is configured to sync from this repository and deploy to the \`rails-app\` namespace.

## Deployment
Changes to the manifests in this repository will be automatically deployed by ArgoCD.
EOF

# Create .gitignore
cat > .gitignore << EOF
# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.swp
EOF

# Add and commit files
git add .
git commit -m "Initial commit: Rails DevOps Kubernetes manifests"

# Create GitHub repository using GitHub CLI or curl
echo "Creating GitHub repository..."
if command -v gh &> /dev/null; then
    # Using GitHub CLI
    gh repo create $REPO_NAME --private --description "GitOps repository for Rails DevOps project"
    git remote add origin $REPO_URL
else
    # Using curl to create repository
    curl -H "Authorization: token $GITHUB_TOKEN" \
         -H "Accept: application/vnd.github.v3+json" \
         https://api.github.com/user/repos \
         -d "{\"name\":\"$REPO_NAME\",\"private\":true,\"description\":\"GitOps repository for Rails DevOps project\"}"
    
    git remote add origin https://$GITHUB_USERNAME:$<EMAIL>/$GITHUB_USERNAME/$REPO_NAME.git
fi

# Push to GitHub
git branch -M main
git push -u origin main

echo "Repository created and pushed to: $REPO_URL"

# Return to original directory
cd - > /dev/null

# Update ArgoCD configurations with actual repository URL
echo "=== Updating ArgoCD configurations ==="
sed -i.bak "s|YOUR_USERNAME|$GITHUB_USERNAME|g" argocd/argocd-cm.yaml
sed -i.bak "s|YOUR_USERNAME|$GITHUB_USERNAME|g" argocd/repository-secret.yaml
sed -i.bak "s|YOUR_USERNAME|$GITHUB_USERNAME|g" argocd/application.yaml

# Update repository secret with GitHub credentials
cat > argocd/repository-secret.yaml << EOF
apiVersion: v1
kind: Secret
metadata:
  name: private-repo
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repository
type: Opaque
stringData:
  type: git
  url: $REPO_URL
  username: $GITHUB_USERNAME
  password: $GITHUB_TOKEN
EOF

# Apply repository secret
kubectl apply -f argocd/repository-secret.yaml

# Apply updated ArgoCD configuration
kubectl apply -f argocd/argocd-cm.yaml

# Create ArgoCD application
kubectl apply -f argocd/application.yaml

echo "=== GitOps Setup Complete ==="
echo ""
echo "Repository: $REPO_URL"
echo "ArgoCD Application: rails-app"
echo ""
echo "Check ArgoCD UI to see the application sync status."
echo "The application should automatically deploy the Rails app and PostgreSQL."

# Cleanup
rm -rf $TEMP_DIR
