apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-rbac-cm
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-rbac-cm
    app.kubernetes.io/part-of: argocd
data:
  # RBAC policy configuration
  policy.default: role:readonly
  
  # Define roles and permissions
  policy.csv: |
    # Admin role - full access
    p, role:admin, applications, *, */*, allow
    p, role:admin, clusters, *, *, allow
    p, role:admin, repositories, *, *, allow
    p, role:admin, certificates, *, *, allow
    p, role:admin, accounts, *, *, allow
    p, role:admin, gpgkeys, *, *, allow
    p, role:admin, logs, *, *, allow
    p, role:admin, exec, *, *, allow
    
    # Developer role - limited access
    p, role:developer, applications, get, rails-app/*, allow
    p, role:developer, applications, sync, rails-app/*, allow
    p, role:developer, applications, action/*, rails-app/*, allow
    p, role:developer, logs, get, rails-app/*, allow
    p, role:developer, repositories, get, *, allow
    
    # ReadOnly role - view only
    p, role:readonly, applications, get, *, allow
    p, role:readonly, repositories, get, *, allow
    p, role:readonly, clusters, get, *, allow
    p, role:readonly, logs, get, *, allow
    
    # Bind admin user to admin role
    g, admin, role:admin
    
    # Example: bind specific users to roles
    # g, <EMAIL>, role:developer
    # g, <EMAIL>, role:readonly
  
  # Scopes for OIDC (if using external authentication)
  scopes: '[groups, email]'
