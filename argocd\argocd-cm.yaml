apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-cm
    app.kubernetes.io/part-of: argocd
data:
  # Server configuration
  url: "https://argocd.local"
  
  # Git repositories configuration
  repositories: |
    - type: git
      url: https://github.com/YOUR_USERNAME/rails-devops-gitops
      name: rails-devops-gitops
      
  # Application configuration
  application.instanceLabelKey: argocd.argoproj.io/instance
  
  # Server configuration
  server.insecure: "true"  # For local development only
  
  # Disable admin user (optional, for production use RBAC)
  accounts.admin: apiKey, login
  
  # Timeout settings
  timeout.hard: 0
  timeout.reconciliation: 180s
  
  # Resource customizations
  resource.customizations: |
    argoproj.io/Application:
      health.lua: |
        hs = {}
        hs.status = "Progressing"
        hs.message = ""
        if obj.status ~= nil then
          if obj.status.health ~= nil then
            hs.status = obj.status.health.status
            if obj.status.health.message ~= nil then
              hs.message = obj.status.health.message
            end
          end
        end
        return hs
