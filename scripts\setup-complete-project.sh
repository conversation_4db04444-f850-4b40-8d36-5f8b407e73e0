#!/bin/bash

# Complete DevOps Project Setup Script
set -e

echo "=========================================="
echo "Ruby on Rails DevOps Project Setup"
echo "=========================================="
echo ""

# Check prerequisites
echo "Checking prerequisites..."

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "Error: kubectl is not installed"
    exit 1
fi

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    echo "Error: docker is not installed"
    exit 1
fi

# Check if cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo "Error: Kubernetes cluster is not accessible"
    exit 1
fi

echo "Prerequisites check passed!"
echo ""

# Get user inputs
read -p "Enter your Docker Hub username: " DOCKER_USERNAME
read -s -p "Enter your Docker Hub password: " DOCKER_PASSWORD
echo ""
read -p "Enter your Docker Hub email: " DOCKER_EMAIL
read -p "Enter your GitHub username: " GITHUB_USERNAME
read -s -p "Enter your GitHub personal access token: " GITHUB_TOKEN
echo ""

export DOCKER_USERNAME
export DOCKER_PASSWORD
export DOCKER_EMAIL
export GITHUB_USERNAME
export GITHUB_TOKEN

echo "=========================================="
echo "Step 1: Building and Deploying Rails App"
echo "=========================================="

# Make scripts executable
chmod +x scripts/*.sh

# Build and deploy Rails application
./scripts/build-and-deploy.sh

echo ""
echo "Step 1 completed successfully!"
echo ""

echo "=========================================="
echo "Step 2: Installing and Configuring ArgoCD"
echo "=========================================="

# Install ArgoCD
./scripts/install-argocd.sh

# Setup GitOps
./scripts/setup-gitops.sh

echo ""
echo "Step 2 completed successfully!"
echo ""

echo "=========================================="
echo "Step 3: Installing and Configuring Tekton"
echo "=========================================="

# Install Tekton
./scripts/install-tekton.sh

# Setup Tekton pipeline
./scripts/setup-tekton-pipeline.sh

echo ""
echo "Step 3 completed successfully!"
echo ""

echo "=========================================="
echo "Setup Complete!"
echo "=========================================="
echo ""
echo "Your DevOps pipeline is now ready!"
echo ""
echo "Access Points:"
echo "1. Rails Application:"
echo "   - http://rails-app.local (add to /etc/hosts)"
echo "   - kubectl port-forward -n rails-app service/rails-app-service 3000:80"
echo ""
echo "2. ArgoCD UI:"
echo "   - http://argocd.local (add to /etc/hosts)"
echo "   - kubectl port-forward svc/argocd-server -n argocd 8080:443"
echo "   - Username: admin"
echo "   - Password: $(kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d)"
echo ""
echo "3. Tekton Dashboard:"
echo "   - kubectl port-forward -n tekton-pipelines service/tekton-dashboard 9097:9097"
echo "   - http://localhost:9097"
echo ""
echo "Repositories Created:"
echo "- Source: https://github.com/$GITHUB_USERNAME/rails-devops-project"
echo "- GitOps: https://github.com/$GITHUB_USERNAME/rails-devops-gitops"
echo ""
echo "Next Steps:"
echo "1. Push your Rails application code to the source repository"
echo "2. Configure GitHub webhook to trigger Tekton pipeline"
echo "3. Test the complete CI/CD workflow"
echo ""
echo "For manual pipeline execution:"
echo "kubectl create -f tekton/pipelinerun.yaml"
