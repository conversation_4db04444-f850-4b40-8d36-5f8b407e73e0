apiVersion: tekton.dev/v1beta1
kind: PipelineRun
metadata:
  generateName: rails-app-pipeline-run-
  namespace: tekton-pipelines
  labels:
    app: rails-app
    pipeline: ci-cd
spec:
  pipelineRef:
    name: rails-app-pipeline
  
  params:
    - name: repo-url
      value: "https://github.com/YOUR_USERNAME/rails-devops-project"
    - name: branch-name
      value: "main"
    - name: image-reference
      value: "YOUR_DOCKER_USERNAME/rails-app"
    - name: dockerfile
      value: "./Dockerfile"
    - name: context
      value: "."
    - name: gitops-repo-url
      value: "https://github.com/YOUR_USERNAME/rails-devops-gitops"
  
  workspaces:
    - name: shared-data
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 1Gi
    
    - name: docker-credentials
      secret:
        secretName: docker-credentials
    
    - name: gitops-data
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 500Mi
  
  serviceAccountName: tekton-build-sa
  
  timeout: "1h0m0s"
