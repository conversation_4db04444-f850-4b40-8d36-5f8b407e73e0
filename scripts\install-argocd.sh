#!/bin/bash

# Install and configure ArgoCD
set -e

echo "=== Installing ArgoCD ==="

# Create namespace
kubectl apply -f argocd/namespace.yaml

# Install ArgoCD
echo "Downloading and installing ArgoCD..."
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml

# Wait for ArgoCD to be ready
echo "Waiting for ArgoCD to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/argocd-server -n argocd
kubectl wait --for=condition=available --timeout=300s deployment/argocd-repo-server -n argocd
kubectl wait --for=condition=available --timeout=300s deployment/argocd-dex-server -n argocd

# Apply custom configurations
echo "Applying ArgoCD configurations..."
kubectl apply -f argocd/argocd-cm.yaml
kubectl apply -f argocd/argocd-rbac-cm.yaml
kubectl apply -f argocd/argocd-server-service.yaml
kubectl apply -f argocd/argocd-ingress.yaml

# Restart ArgoCD server to pick up new configurations
echo "Restarting ArgoCD server..."
kubectl rollout restart deployment/argocd-server -n argocd
kubectl rollout status deployment/argocd-server -n argocd

# Get initial admin password
echo "=== ArgoCD Installation Complete ==="
echo ""
echo "Getting initial admin password..."
ARGOCD_PASSWORD=$(kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d)
echo "ArgoCD Admin Password: $ARGOCD_PASSWORD"
echo ""
echo "Access ArgoCD:"
echo "1. Add to /etc/hosts: 127.0.0.1 argocd.local"
echo "2. Access: http://argocd.local (via ingress)"
echo "3. Or use port-forward: kubectl port-forward svc/argocd-server -n argocd 8080:443"
echo "4. Or use NodePort: http://localhost:30080"
echo ""
echo "Login credentials:"
echo "Username: admin"
echo "Password: $ARGOCD_PASSWORD"
echo ""
echo "Next steps:"
echo "1. Create a private GitHub repository named 'rails-devops-gitops'"
echo "2. Push the k8s/ directory to the repository"
echo "3. Update argocd/repository-secret.yaml with your GitHub credentials"
echo "4. Apply the repository secret: kubectl apply -f argocd/repository-secret.yaml"
echo "5. Update argocd/application.yaml with your repository URL"
echo "6. Create the application: kubectl apply -f argocd/application.yaml"
