<div class="posts-header">
  <h1>All Posts</h1>
  <%= link_to "New Post", new_post_path, class: "btn btn-primary" %>
</div>

<% if @posts.any? %>
  <div class="posts-list">
    <% @posts.each do |post| %>
      <div class="post-item">
        <h3><%= link_to post.title, post %></h3>
        <p><%= truncate(post.content, length: 150) %></p>
        <div class="post-actions">
          <%= link_to "View", post, class: "btn btn-sm btn-outline" %>
          <%= link_to "Edit", edit_post_path(post), class: "btn btn-sm btn-outline" %>
          <%= link_to "Delete", post, method: :delete, 
                      confirm: "Are you sure?", 
                      class: "btn btn-sm btn-danger" %>
        </div>
        <small>Created: <%= post.created_at.strftime("%B %d, %Y at %I:%M %p") %></small>
      </div>
    <% end %>
  </div>
<% else %>
  <div class="empty-state">
    <p>No posts found.</p>
    <%= link_to "Create your first post", new_post_path, class: "btn btn-primary" %>
  </div>
<% end %>
