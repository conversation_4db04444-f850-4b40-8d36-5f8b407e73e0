#!/bin/bash

# Setup Tekton Pipeline for Rails App
set -e

# Check if required environment variables are set
if [ -z "$DOCKER_USERNAME" ] || [ -z "$DOCKER_PASSWORD" ] || [ -z "$GITHUB_USERNAME" ]; then
    echo "Error: Please set required environment variables"
    echo "Example:"
    echo "export DOCKER_USERNAME=your-docker-username"
    echo "export DOCKER_PASSWORD=your-docker-password"
    echo "export GITHUB_USERNAME=your-github-username"
    echo "export DOCKER_EMAIL=<EMAIL> (optional)"
    exit 1
fi

DOCKER_EMAIL=${DOCKER_EMAIL:-"$<EMAIL>"}

echo "=== Setting up Tekton Pipeline ==="
echo "Docker Username: $DOCKER_USERNAME"
echo "GitHub Username: $GITHUB_USERNAME"

# Create Docker Hub credentials secret
echo "Creating Docker Hub credentials..."
kubectl create secret docker-registry docker-credentials \
  --docker-server=https://index.docker.io/v1/ \
  --docker-username=$DOCKER_USERNAME \
  --docker-password=$DOCKER_PASSWORD \
  --docker-email=$DOCKER_EMAIL \
  --namespace=tekton-pipelines \
  --dry-run=client -o yaml | kubectl apply -f -

# Create service account and RBAC
echo "Creating service account and RBAC..."
kubectl apply -f tekton/service-account.yaml

# Update pipeline configurations with actual values
echo "Updating pipeline configurations..."
sed -i.bak "s|YOUR_DOCKER_USERNAME|$DOCKER_USERNAME|g" tekton/pipeline.yaml
sed -i.bak "s|YOUR_USERNAME|$GITHUB_USERNAME|g" tekton/pipeline.yaml
sed -i.bak "s|YOUR_DOCKER_USERNAME|$DOCKER_USERNAME|g" tekton/pipelinerun.yaml
sed -i.bak "s|YOUR_USERNAME|$GITHUB_USERNAME|g" tekton/pipelinerun.yaml

# Update trigger configurations
sed -i.bak "s|YOUR_DOCKER_USERNAME|$DOCKER_USERNAME|g" tekton/triggers/trigger-template.yaml
sed -i.bak "s|YOUR_USERNAME|$GITHUB_USERNAME|g" tekton/triggers/trigger-template.yaml
sed -i.bak "s|YOUR_DOCKER_USERNAME|$DOCKER_USERNAME|g" tekton/triggers/trigger-binding.yaml
sed -i.bak "s|YOUR_USERNAME|$GITHUB_USERNAME|g" tekton/triggers/trigger-binding.yaml

# Create tasks
echo "Creating Tekton tasks..."
kubectl apply -f tekton/tasks/

# Create pipeline
echo "Creating Tekton pipeline..."
kubectl apply -f tekton/pipeline.yaml

# Create webhook secret
echo "Creating GitHub webhook secret..."
WEBHOOK_SECRET=$(openssl rand -hex 20)
sed -i.bak "s|your-webhook-secret-token-here|$WEBHOOK_SECRET|g" tekton/triggers/github-webhook-secret.yaml
kubectl apply -f tekton/triggers/github-webhook-secret.yaml

# Create triggers
echo "Creating Tekton triggers..."
kubectl apply -f tekton/triggers/

echo "=== Tekton Pipeline Setup Complete ==="
echo ""
echo "Pipeline Configuration:"
echo "- Docker Image: $DOCKER_USERNAME/rails-app"
echo "- Source Repository: https://github.com/$GITHUB_USERNAME/rails-devops-project"
echo "- GitOps Repository: https://github.com/$GITHUB_USERNAME/rails-devops-gitops"
echo "- Webhook Secret: $WEBHOOK_SECRET"
echo ""
echo "Manual Pipeline Run:"
echo "kubectl create -f tekton/pipelinerun.yaml"
echo ""
echo "Access Tekton Dashboard:"
echo "kubectl port-forward -n tekton-pipelines service/tekton-dashboard 9097:9097"
echo "Then open: http://localhost:9097"
echo ""
echo "Webhook URL for GitHub:"
echo "http://your-cluster-ip:30081"
echo ""
echo "To get the webhook URL:"
echo "kubectl get service el-rails-app-event-listener -n tekton-pipelines"
