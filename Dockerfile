# Use Ruby 3.1.0 as base image
FROM ruby:3.1.0

# Install dependencies
RUN apt-get update -qq && apt-get install -y \
    nodejs \
    npm \
    postgresql-client \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Yarn
RUN npm install -g yarn

# Set working directory
WORKDIR /app

# Copy Gemfile and Gemfile.lock
COPY Gemfile Gemfile.lock ./

# Install gems
RUN bundle install

# Copy package.json if it exists (for JavaScript dependencies)
COPY package*.json yarn.lock* ./

# Install JavaScript dependencies if package.json exists
RUN if [ -f "package.json" ]; then yarn install; fi

# Copy the rest of the application
COPY . .

# Create directories for Rails
RUN mkdir -p tmp/pids tmp/cache tmp/sockets log

# Precompile assets (if needed)
RUN if [ "$RAILS_ENV" = "production" ]; then \
    bundle exec rails assets:precompile; \
    fi

# Create a script to wait for database and run migrations
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Expose port 3000
EXPOSE 3000

# Set the entrypoint
ENTRYPOINT ["docker-entrypoint.sh"]

# Default command
CMD ["rails", "server", "-b", "0.0.0.0"]
